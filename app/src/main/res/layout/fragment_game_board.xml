<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/gameBoardRoot"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context="at.aau.se2.cluedo.viewmodels.GameBoard">

        <at.aau.se2.cluedo.viewmodels.GameBoard
            android:id="@+id/gameBoardView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/cardsOpenButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="48dp"
            android:contentDescription="TODO"
            android:src="@drawable/cards_open_icon"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.499"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/diceOneValueTextView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:text="?"
            android:textSize="48sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.009"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/diceTwoValueTextView2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:text="?"
            android:textSize="48sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.044"
            app:layout_constraintStart_toEndOf="@+id/diceOneValueTextView2"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/solveCaseButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:text="Solve"
            app:layout_constraintBottom_toTopOf="@+id/makeSuspicionButton"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/makeSuspicionButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="64dp"
            android:enabled="false"
            android:text="Suspect"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent" />


        <Button
            android:id="@+id/notesButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Notes"
            app:layout_constraintBottom_toTopOf="@+id/solveCaseButton"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/diceTwoValueTextView2" />

        <Button
            android:id="@+id/movebutton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="56dp"
            android:text="Move"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.295"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/roll_dice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="56dp"
            android:text="@string/roll_dice"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.719"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/skipTurnButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="Skip Turn"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.719"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/roll_dice" />

        <Button
            android:id="@+id/moveDown"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Down"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.501"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/moveUp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:text="Up"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/Done"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.498"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/Done"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:text="Done"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/moveDown"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.498"
            app:layout_constraintStart_toStartOf="parent" />

        <Button
            android:id="@+id/moveRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="88dp"
            android:text="Right"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.506"
            app:layout_constraintStart_toEndOf="@+id/moveDown" />

        <Button
            android:id="@+id/moveLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="88dp"
            android:text="Left"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/moveDown"
            app:layout_constraintStart_toStartOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- BOTTOM SHEET AS DIRECT CHILD OF COORDINATORLAYOUT -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/bottom_sheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/cards_background"
        android:clipToPadding="true"
        android:paddingBottom="8dp"
        app:behavior_hideable="true"
        app:behavior_peekHeight="0dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingTop="16dp"
            android:paddingEnd="16dp">

            <View
                android:layout_width="40dp"
                android:layout_height="4dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="16dp"
                android:background="@android:color/darker_gray" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Player Cards"
                android:textSize="20sp"
                android:textStyle="bold" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/player_cards_recyclerview"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingBottom="8dp" />

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
